<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Portfolio - Essential Property Services</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Merriweather:ital,wght@0,400;0,700;1,400&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#4B0082",
              accent: "#FFD700",
            },
            fontFamily: {
              sans: ['"Inter"', "system-ui", "-apple-system", "sans-serif"],
              serif: ['"Merriweather"', "Georgia", "serif"],
            },
          },
        },
      };
    </script>
    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .animate-fadeIn {
        animation: fadeIn 0.5s ease-out forwards;
      }

      .hover-grow {
        transition: transform 0.2s ease;
      }
      .hover-grow:hover {
        transform: scale(1.02);
      }

      input:focus,
      select:focus,
      textarea:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.2);
      }

      .hide-scrollbar::-webkit-scrollbar {
        display: none;
      }

      .hide-scrollbar {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }

      .before-after-slider {
        position: relative;
        width: 100%;
        height: 300px;
        overflow: hidden;
        cursor: ew-resize;
        border-radius: 0.5rem;
        user-select: none;
      }

      .before-after-slider img {
        pointer-events: none;
        user-select: none;
        -webkit-user-drag: none;
      }

      .slider-handle {
        position: absolute;
        top: 50%;
        width: 32px;
        height: 32px;
        background: #ffd700;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        pointer-events: none;
      }
    </style>
    <script>
      !(function (Gleap, t, i) {
        if (!(Gleap = window.Gleap = window.Gleap || []).invoked) {
          for (
            window.GleapActions = [],
              Gleap.invoked = !0,
              Gleap.methods = [
                "identify",
                "setEnvironment",
                "setTags",
                "attachCustomData",
                "setCustomData",
                "removeCustomData",
                "clearCustomData",
                "registerCustomAction",
                "trackEvent",
                "setUseCookies",
                "log",
                "preFillForm",
                "showSurvey",
                "sendSilentCrashReport",
                "startFeedbackFlow",
                "startBot",
                "setAppBuildNumber",
                "setAppVersionCode",
                "setApiUrl",
                "setFrameUrl",
                "isOpened",
                "open",
                "close",
                "on",
                "setLanguage",
                "setOfflineMode",
                "startClassicForm",
                "initialize",
                "disableConsoleLogOverwrite",
                "logEvent",
                "hide",
                "enableShortcuts",
                "showFeedbackButton",
                "destroy",
                "getIdentity",
                "isUserIdentified",
                "clearIdentity",
                "openConversations",
                "openConversation",
                "openHelpCenterCollection",
                "openHelpCenterArticle",
                "openHelpCenter",
                "searchHelpCenter",
                "openNewsArticle",
                "openChecklists",
                "startChecklist",
                "openNews",
                "openFeatureRequests",
                "isLiveMode",
              ],
              Gleap.f = function (e) {
                return function () {
                  var t = Array.prototype.slice.call(arguments);
                  window.GleapActions.push({ e: e, a: t });
                };
              },
              t = 0;
            t < Gleap.methods.length;
            t++
          )
            Gleap[(i = Gleap.methods[t])] = Gleap.f(i);
          (Gleap.load = function () {
            var t = document.getElementsByTagName("head")[0],
              i = document.createElement("script");
            (i.type = "text/javascript"),
              (i.async = !0),
              (i.src = "https://sdk.gleap.io/latest/index.js"),
              t.appendChild(i);
          }),
            Gleap.load(),
            Gleap.initialize("AWgSKIC1b9ZgFMyt29I0VWOUUYxQSfJo");
        }
      })();
    </script>
  </head>
  <body class="font-sans text-gray-800">
    <!-- Header -->
    <header
      class="fixed w-full z-50 transition-all duration-300 py-4"
      id="header"
    >
      <div class="container mx-auto px-4 md:px-6">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <a href="index.html">
              <img
                src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681586cfdb0184d1a887b9de.svg"
                alt="Essential Property Services Logo"
                class="h-12 md:h-16"
              />
            </a>
          </div>

          <div class="hidden md:flex items-center space-x-8">
            <nav>
              <ul class="flex space-x-6">
                <li>
                  <a
                    href="services.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Services</a
                  >
                </li>
                <li>
                  <a
                    href="portfolio.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Portfolio</a
                  >
                </li>
                <li>
                  <a
                    href="team.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Our Team</a
                  >
                </li>
                <li>
                  <a
                    href="index.html#contact"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Contact</a
                  >
                </li>
              </ul>
            </nav>

            <a
              href="tel:9076009900"
              class="flex items-center bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md font-medium transition"
            >
              <svg
                class="w-[18px] h-[18px] mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                />
              </svg>
              (*************
            </a>
          </div>

          <button class="md:hidden text-primary" onclick="toggleMobileMenu()">
            <svg
              class="w-7 h-7"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div id="mobileMenu" class="hidden md:hidden bg-white shadow-lg">
        <nav class="container mx-auto px-4 py-4">
          <ul class="space-y-4">
            <li>
              <a
                href="services.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Services
              </a>
            </li>
            <li>
              <a
                href="portfolio.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Portfolio
              </a>
            </li>
            <li>
              <a
                href="team.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Our Team
              </a>
            </li>
            <li>
              <a
                href="index.html#contact"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Contact
              </a>
            </li>
            <li>
              <a
                href="tel:9076009900"
                class="flex items-center w-full bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-medium justify-center transition"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                (*************
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <div class="min-h-screen bg-gray-50">
      <!-- Hero Section -->
      <section class="relative h-[60vh] flex items-center">
        <div class="absolute inset-0">
          <img
            src="https://images.pexels.com/photos/1643383/pexels-photo-1643383.jpeg"
            alt="Portfolio Hero"
            class="w-full h-full object-cover"
          />
          <div class="absolute inset-0 bg-black opacity-60"></div>
        </div>
        <div class="container mx-auto px-4 relative z-10">
          <div class="max-w-4xl mx-auto text-center text-white">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">
              Transforming Anchorage Homes
            </h1>
            <p class="text-xl mb-8 opacity-90">
              Browse through our portfolio of successful projects and see why
              we're Anchorage's most trusted contractor.
            </p>
            <div class="flex flex-wrap gap-4 justify-center items-center">
              <div
                class="flex items-center bg-primary/80 px-4 py-2 rounded-full"
              >
                <svg
                  class="w-5 h-5 text-accent mr-2"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <polygon
                    points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
                  />
                </svg>
                <span class="text-white font-semibold">5/5 Average Rating</span>
              </div>
              <div
                class="flex items-center bg-primary/80 px-4 py-2 rounded-full"
              >
                <span class="text-white font-semibold"
                  >Hundreds of Happy Clients</span
                >
              </div>
              <div
                class="flex items-center bg-primary/80 px-4 py-2 rounded-full"
              >
                <span class="text-white font-semibold">Emergency Services</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Projects Grid -->
      <section class="py-20">
        <div class="container mx-auto px-4">
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            id="projectsGrid"
          >
            <!-- Projects will be dynamically inserted here -->
          </div>
        </div>
      </section>

      <!-- Instagram Videos Section -->
      <section class="py-16 bg-primary/5">
        <div class="container mx-auto px-4">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-primary mb-4">
              Watch Our Transformations
            </h2>
            <p class="text-gray-600">
              Follow our journey on Instagram for more inspiration
            </p>
          </div>

          <div class="relative">
            <button
              onclick="scrollVideos('left')"
              class="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 p-2 rounded-full shadow-lg hover:bg-white transition-colors"
            >
              <svg
                class="w-6 h-6 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>

            <div
              id="videoScroll"
              class="flex overflow-x-auto gap-6 pb-6 scroll-smooth hide-scrollbar"
              style="scroll-behavior: smooth"
            >
              <!-- Videos will be dynamically inserted here -->
            </div>

            <button
              onclick="scrollVideos('right')"
              class="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 p-2 rounded-full shadow-lg hover:bg-white transition-colors"
            >
              <svg
                class="w-6 h-6 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </div>
        </div>
      </section>

      <!-- CTA Section -->
      <section class="py-16 bg-primary">
        <div class="container mx-auto px-4">
          <div class="max-w-4xl mx-auto text-center text-white">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">
              Ready to Start Your Dream Project?
            </h2>
            <p class="text-xl mb-8 opacity-90">
              Join our growing list of satisfied homeowners and experience the
              Essential Property Services difference.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="index.html#contact"
                class="px-8 py-4 bg-accent hover:bg-accent/90 text-primary font-semibold rounded-md transition"
              >
                Get Your Free Estimate
              </a>
              <a
                href="tel:9076009900"
                class="px-8 py-4 bg-white hover:bg-gray-100 text-primary font-semibold rounded-md transition flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                Call Now: (*************
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>

    <script>
      // Header scroll effect
      window.addEventListener("scroll", () => {
        const header = document.getElementById("header");
        const navLinks = document.querySelectorAll(".nav-link");

        if (window.scrollY > 20) {
          header.classList.remove("py-4");
          header.classList.add("bg-white", "shadow-md", "py-2");
          navLinks.forEach((link) => {
            link.classList.remove("text-white");
            link.classList.add("text-gray-800");
          });
        } else {
          header.classList.add("py-4");
          header.classList.remove("bg-white", "shadow-md", "py-2");
          navLinks.forEach((link) => {
            link.classList.add("text-white");
            link.classList.remove("text-gray-800");
          });
        }
      });

      // Mobile menu toggle
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        mobileMenu.classList.toggle("hidden");
      }

      function closeMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        mobileMenu.classList.add("hidden");
      }

      // Projects data
      const projects = [
        {
          id: 1,
          title: "Modern Kitchen Transformation",
          description:
            "Complete kitchen remodel with custom cabinets, quartz countertops, and new appliances",
          type: "Kitchen Remodel",
          beforeImage:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68159806db01842adf87ca4f.webp",
          afterImage:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68159806ea8101223fed35dc.webp",
          testimonial: {
            text: "They transformed our outdated kitchen into a modern masterpiece. The attention to detail was incredible!",
            author: "Sarah M.",
            rating: 5,
          },
        },
        {
          id: 2,
          title: "Gorgeous Bathroom Renovation",
          description:
            "Luxury bathroom remodel with heated floors, custom tile work, and modern fixtures",
          type: "Bathroom Remodel",
          beforeImage:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/6815a2f00013197527049d0b.webp",
          afterImage:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/6815a2f0db0184080087d5c1.webp",
          testimonial: {
            text: "Our bathroom feels like a high-end spa now. The heated floors are amazing!",
            author: "Michael & Lisa K.",
            rating: 5,
          },
        },
        {
          id: 3,
          title: "Custom Deck Build",
          description:
            "New gorgeous deck with dark composite decking and cable railing",
          type: "Outdoor Living",
          beforeImage:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/6815a5ceea81010f18ed4746.webp",
          afterImage:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/6815a5ceca200059e5301abc.webp",
          testimonial: {
            text: "The new deck completely transformed our backyard. Perfect for entertaining!",
            author: "David W.",
            rating: 5,
          },
        },
        {
          id: 4,
          title: "Flooring Installation",
          description:
            "Complete kitchen and hallway transformation with new flooring",
          type: "Flooring Installation",
          beforeImage:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68152dd5209d22cd672d22f3.webp",
          afterImage:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68152dd55d4a83c03f067435.webp",
          testimonial: {
            text: "The new floor is so much better than the old one. We're so grateful to have this finally fixed.",
            author: "John Doe",
            rating: 5,
          },
        },
        {
          id: 5,
          title: "Master Suite Addition",
          description: "400 sq ft master suite addition with custom closets",
          type: "Home Addition",
          beforeImage:
            "https://images.pexels.com/photos/1648768/pexels-photo-1648768.jpeg",
          afterImage:
            "https://images.pexels.com/photos/1743229/pexels-photo-1743229.jpeg",
          testimonial: {
            text: "The new master suite exceeded our expectations. It's like having a luxury hotel room at home!",
            author: "Emily R.",
            rating: 5,
          },
        },
        {
          id: 6,
          title: "Complete Home Exterior",
          description: "New siding, windows, and roof with modern color scheme",
          type: "Exterior Renovation",
          beforeImage:
            "https://images.pexels.com/photos/280222/pexels-photo-280222.jpeg",
          afterImage:
            "https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg",
          testimonial: {
            text: "Our home's curb appeal has improved dramatically. The neighbors keep asking for your number!",
            author: "Thomas & Mary B.",
            rating: 5,
          },
        },
        {
          id: 7,
          title: "Open Concept Living",
          description:
            "Removed walls to create an open floor plan with modern finishes",
          type: "Interior Renovation",
          beforeImage:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/6815acfcea81016307ed4d64.webp",
          afterImage:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/6815acfc1e3c1e5e4112e2d4.webp",
          testimonial: {
            text: "The open concept transformation has made our home feel so much larger and brighter.",
            author: "Amanda L.",
            rating: 5,
          },
        },
        {
          id: 8,
          title: "Garage Conversion",
          description:
            "Here's what a plain garage looks like when it's converted into a beautiful home gym",
          type: "Space Conversion",
          beforeImage:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/6815ab1dea81010787ed4b82.webp",
          afterImage:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/6815ab1dea8101b542ed4b83.webp",
          testimonial: {
            text: "The garage conversion was brilliant. Now we have a perfect work-from-home setup!",
            author: "Chris P.",
            rating: 5,
          },
        },
        {
          id: 9,
          title: "Luxury Kitchen Remodel",
          description:
            "High-end kitchen renovation with professional-grade appliances",
          type: "Kitchen Remodel",
          beforeImage:
            "https://images.pexels.com/photos/1643384/pexels-photo-1643384.jpeg",
          afterImage:
            "https://images.pexels.com/photos/3214064/pexels-photo-3214064.jpeg",
          testimonial: {
            text: "This kitchen is a chef's dream! The quality of work is outstanding.",
            author: "Patricia M.",
            rating: 5,
          },
        },
      ];

      // Instagram videos data
      const instagramVideos = [
        {
          id: 1,
          thumbnail:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68152de9ca200088432f72e9.webp",
          videoUrl: "#",
          title: "Bathroom Transformation Timelapse",
          views: "2.4K views",
        },
        {
          id: 2,
          thumbnail:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68152de988cf5c168af6431b.webp",
          videoUrl: "#",
          title: "Bathroom Remodel Process",
          views: "1.8K views",
        },
        {
          id: 3,
          thumbnail:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681468f25d4a83742002f13f.webp",
          videoUrl: "#",
          title: "Exterior Repair",
          views: "3.2K views",
        },
        {
          id: 4,
          thumbnail:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68152fe7ca200088882f750d.webp",
          videoUrl: "#",
          title: "Flood Remediation",
          views: "1.5K views",
        },
        {
          id: 5,
          thumbnail:
            "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681468f25d4a83484302f13c.webp",
          videoUrl: "#",
          title: "General Handyman",
          views: "2.1K views",
        },
      ];

      // Before/After Slider functionality
      class BeforeAfterSlider {
        constructor(element) {
          this.element = element;
          this.beforeImage = element.querySelector(".before-image");
          this.isDragging = false;
          this.sliderPosition = 50;

          this.element.addEventListener(
            "mousedown",
            this.startDragging.bind(this)
          );
          document.addEventListener("mousemove", this.drag.bind(this));
          document.addEventListener("mouseup", this.stopDragging.bind(this));

          // Initial render
          this.updateSliderPosition(50);
        }

        startDragging(e) {
          e.preventDefault();
          this.isDragging = true;
          this.element.style.cursor = "grabbing";
        }

        stopDragging() {
          this.isDragging = false;
          this.element.style.cursor = "ew-resize";
        }

        drag(e) {
          if (!this.isDragging) return;

          const rect = this.element.getBoundingClientRect();
          const x = Math.max(0, Math.min(e.clientX - rect.left, rect.width));
          const percent = (x / rect.width) * 100;

          this.updateSliderPosition(percent);
        }

        updateSliderPosition(percent) {
          this.sliderPosition = percent;
          this.beforeImage.style.clipPath = `inset(0 ${100 - percent}% 0 0)`;
          const handle = this.element.querySelector(".slider-handle");
          if (handle) {
            handle.style.left = `${percent}%`;
          }
        }
      }

      // Initialize projects
      function initializeProjects() {
        const projectsGrid = document.getElementById("projectsGrid");

        projects.forEach((project) => {
          const projectElement = document.createElement("div");
          projectElement.className =
            "bg-white rounded-lg shadow-lg overflow-hidden";
          projectElement.innerHTML = `
          <div class="before-after-slider">
            <img src="${
              project.afterImage
            }" alt="After" class="w-full h-full object-cover" />
            <div class="before-image absolute inset-0">
              <img src="${
                project.beforeImage
              }" alt="Before" class="w-full h-full object-cover" />
            </div>
            <div class="slider-handle">
              <svg class="w-6 h-6 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </div>
          </div>
          <div class="p-6">
            <div class="flex items-center justify-between mb-3">
              <span class="text-sm font-medium text-primary">${
                project.type
              }</span>
              <div class="flex">
                ${Array(project.testimonial.rating)
                  .fill()
                  .map(
                    () => `
                  <svg class="w-4 h-4 text-primary fill-current" viewBox="0 0 24 24">
                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/>
                  </svg>
                `
                  )
                  .join("")}
              </div>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">${
              project.title
            }</h3>
            <p class="text-gray-600 mb-4">${project.description}</p>
            <blockquote class="italic text-sm text-gray-500 border-l-4 border-primary pl-4">
              "${project.testimonial.text}"
              <footer class="mt-2 font-medium text-gray-700">
                - ${project.testimonial.author}
              </footer>
            </blockquote>
          </div>
        `;

          projectsGrid.appendChild(projectElement);
          new BeforeAfterSlider(
            projectElement.querySelector(".before-after-slider")
          );
        });
      }

      // Initialize Instagram videos
      function initializeVideos() {
        const videoScroll = document.getElementById("videoScroll");

        instagramVideos.forEach((video) => {
          const videoElement = document.createElement("div");
          videoElement.className =
            "flex-none w-[300px] bg-white rounded-lg shadow-lg overflow-hidden group";
          videoElement.innerHTML = `
          <div class="relative aspect-[9/16] bg-gray-100">
            <img
              src="${video.thumbnail}"
              alt="${video.title}"
              class="w-full h-full object-cover"
            />
            <div class="absolute inset-0 bg-primary/20 group-hover:bg-primary/30 transition-colors">
              <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/60 to-transparent">
                <h3 class="text-white font-semibold">${video.title}</h3>
                <p class="text-white/80 text-sm">${video.views}</p>
              </div>
            </div>
          </div>
        `;

          videoScroll.appendChild(videoElement);
        });
      }

      // Video scroll functionality
      function scrollVideos(direction) {
        const videoScroll = document.getElementById("videoScroll");
        const scrollAmount = 300;
        videoScroll.scrollLeft +=
          direction === "left" ? -scrollAmount : scrollAmount;
      }

      // Initialize everything when the page loads
      document.addEventListener("DOMContentLoaded", () => {
        initializeProjects();
        initializeVideos();
      });
    </script>
  </body>
  <!-- Footer -->
  <footer class="bg-primary text-white py-12">
    <div class="container mx-auto px-4 md:px-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
        <div>
          <img
            src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681586cfdb0184d1a887b9de.svg"
            alt="Essential Property Services Logo"
            class="h-16 mb-4"
          />
          <p class="text-gray-300 mb-4">
            Created to serve with excellence, through humility and
            professionalism.
          </p>
          <div class="flex space-x-4">
            <a href="#" class="text-accent hover:text-accent/80 transition">
              <span class="sr-only">Facebook</span>
              <svg
                class="w-6 h-6"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                ></path>
              </svg>
            </a>
            <a href="#" class="text-accent hover:text-accent/80 transition">
              <span class="sr-only">Instagram</span>
              <svg
                class="w-6 h-6"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                ></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
            </a>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-semibold mb-4">Contact Information</h3>
          <ul class="space-y-3">
            <li class="flex items-center">
              <svg
                class="w-[18px] h-[18px] mr-3 text-accent"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                ></path>
              </svg>
              <a href="tel:9076009900" class="hover:text-accent transition"
                >(*************</a
              >
            </li>
            <li class="flex items-center">
              <svg
                class="w-[18px] h-[18px] mr-3 text-accent"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                ></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
              <a
                href="mailto:<EMAIL>"
                class="hover:text-accent transition"
                ><EMAIL></a
              >
            </li>
            <li class="flex items-start">
              <svg
                class="w-[18px] h-[18px] mr-3 text-accent mt-1"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
              <span>Serving Anchorage, AK & Surrounding Areas</span>
            </li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold mb-4">Licensing & Information</h3>
          <ul class="space-y-2 text-gray-300">
            <li>Alaska General Contractor License 217482</li>
            <li>MOA License CON14200</li>
            <li>Fully Insured and Bonded</li>
            <li>Available 24/7/364</li>
          </ul>
        </div>
      </div>

      <div
        class="border-t border-gray-700 pt-8 mt-8 text-gray-300 text-sm text-center"
      >
        <p>
          ©
          <script>
            document.write(new Date().getFullYear());
          </script>
          Essential Property Services LLC. All rights reserved.
        </p>
        <p class="mt-2">
          <a href="#" class="hover:text-accent transition">Privacy Policy</a>
          {' | '}
          <a href="#" class="hover:text-accent transition">Terms of Service</a>
        </p>
      </div>
    </div>
  </footer>
</html>
